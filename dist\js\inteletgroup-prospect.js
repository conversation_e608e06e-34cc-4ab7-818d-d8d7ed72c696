// JavaScript para el formulario de prospectos InteletGroup

// Variables globales
let currentUserName = '';
let currentUserId = 0;

// Inicialización cuando el DOM está listo
document.addEventListener('DOMContentLoaded', function() {
    initializeInteletGroupProspectForm();
});

// Función principal de inicialización
function initializeInteletGroupProspectForm() {
    // Obtener información del usuario actual
    if (typeof window.currentUserName !== 'undefined') {
        currentUserName = window.currentUserName;
    }
    if (typeof window.currentUserId !== 'undefined') {
        currentUserId = window.currentUserId;
    }

    // Log de inicialización
    console.log('=== INICIALIZACIÓN INTELETGROUP ===');
    console.log('currentUserName inicializado:', currentUserName);
    console.log('currentUserId inicializado:', currentUserId);
    console.log('window.currentUserName:', window.currentUserName);
    console.log('window.currentUserId:', window.currentUserId);

    // Configurar eventos
    setupFormEvents();
    setupValidation();
    setupFileUpload();
}

// Configurar eventos del formulario
function setupFormEvents() {
    // Botón de guardar
    const saveBtn = document.getElementById('saveInteletGroupProspectBtn');
    if (saveBtn) {
        saveBtn.addEventListener('click', handleSaveProspect);
    }

    // Botón de llenar datos de prueba
    const fillTestBtn = document.getElementById('fillTestDataBtn');
    if (fillTestBtn) {
        fillTestBtn.addEventListener('click', fillTestData);
    }

    // Evento cuando se abre el modal
    const modal = document.getElementById('inteletGroupProspectModal');
    console.log('Modal encontrado:', modal);
    if (modal) {
        console.log('Registrando evento show.bs.modal');
        modal.addEventListener('show.bs.modal', function() {
            console.log('Modal show.bs.modal disparado');
            resetForm();
            populateExecutiveName();
        });
    } else {
        console.log('Modal inteletGroupProspectModal no encontrado');
    }
}

// Configurar validación en tiempo real
function setupValidation() {
    const form = document.getElementById('inteletGroupProspectForm');
    if (!form) return;

    // RUT validation
    const rutField = document.getElementById('rut_cliente');
    if (rutField) {
        rutField.addEventListener('input', function() {
            validateRUT(this);
        });
    }

    // Razón Social validation
    const razonSocialField = document.getElementById('razon_social');
    if (razonSocialField) {
        razonSocialField.addEventListener('input', function() {
            validateRazonSocial(this);
        });
    }

    // Teléfono validation
    const telefonoField = document.getElementById('telefono_celular');
    if (telefonoField) {
        telefonoField.addEventListener('input', function() {
            validateTelefono(this);
        });
    }

    // Email validation
    const emailField = document.getElementById('email');
    if (emailField) {
        emailField.addEventListener('input', function() {
            validateEmail(this);
        });
    }

    // Validación general para campos requeridos
    const requiredFields = form.querySelectorAll('[required]');
    requiredFields.forEach(field => {
        field.addEventListener('blur', function() {
            validateRequired(this);
        });
    });
}

// Configurar subida de archivos
function setupFileUpload() {
    const fileInput = document.getElementById('documentos');
    if (!fileInput) return;

    fileInput.addEventListener('change', function() {
        validateFiles(this);
    });
}

// Validar RUT
function validateRUT(field) {
    const value = field.value.trim();
    const rutPattern = /^\d{7,8}-[\dkK]$/;
    
    if (value === '') {
        setFieldState(field, 'neutral');
        return true;
    }
    
    if (!rutPattern.test(value)) {
        setFieldState(field, 'invalid', 'Formato inválido. Use: ********-9');
        return false;
    }
    
    setFieldState(field, 'valid');
    return true;
}

// Validar Razón Social
function validateRazonSocial(field) {
    const value = field.value.trim();
    const pattern = /^[A-Z\s]+$/;
    
    if (value === '') {
        setFieldState(field, 'neutral');
        return true;
    }
    
    if (!pattern.test(value)) {
        setFieldState(field, 'invalid', 'Solo letras mayúsculas y espacios');
        return false;
    }
    
    setFieldState(field, 'valid');
    return true;
}

// Validar teléfono
function validateTelefono(field) {
    const value = field.value.trim();
    const pattern = /^\d{9,15}$/;
    
    if (value === '') {
        setFieldState(field, 'neutral');
        return true;
    }
    
    if (!pattern.test(value)) {
        setFieldState(field, 'invalid', 'Solo números, mínimo 9 dígitos');
        return false;
    }
    
    setFieldState(field, 'valid');
    return true;
}

// Validar email
function validateEmail(field) {
    const value = field.value.trim();
    const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (value === '') {
        setFieldState(field, 'neutral');
        return true;
    }
    
    if (!pattern.test(value)) {
        setFieldState(field, 'invalid', 'Formato de email inválido');
        return false;
    }
    
    setFieldState(field, 'valid');
    return true;
}

// Validar campos requeridos
function validateRequired(field) {
    const value = field.value.trim();
    
    if (value === '') {
        setFieldState(field, 'invalid', 'Este campo es requerido');
        return false;
    }
    
    setFieldState(field, 'valid');
    return true;
}

// Validar archivos
function validateFiles(fileInput) {
    const files = fileInput.files;
    const maxSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'image/jpeg',
        'image/jpg',
        'image/png',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];
    
    let isValid = true;
    let errorMessage = '';
    
    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        if (file.size > maxSize) {
            isValid = false;
            errorMessage = `El archivo ${file.name} excede el tamaño máximo de 5MB`;
            break;
        }
        
        if (!allowedTypes.includes(file.type)) {
            isValid = false;
            errorMessage = `El archivo ${file.name} no tiene un formato permitido`;
            break;
        }
    }
    
    if (!isValid) {
        setFieldState(fileInput, 'invalid', errorMessage);
        fileInput.value = '';
    } else {
        setFieldState(fileInput, 'valid');
    }
    
    return isValid;
}

// Establecer estado del campo
function setFieldState(field, state, message = '') {
    const feedback = field.parentNode.querySelector('.invalid-feedback');
    
    // Limpiar clases anteriores
    field.classList.remove('is-valid', 'is-invalid');
    
    switch (state) {
        case 'valid':
            field.classList.add('is-valid');
            if (feedback) feedback.textContent = '';
            break;
        case 'invalid':
            field.classList.add('is-invalid');
            if (feedback) feedback.textContent = message;
            break;
        case 'neutral':
            if (feedback) feedback.textContent = '';
            break;
    }
}

// Poblar nombre del ejecutivo
function populateExecutiveName() {
    console.log('=== DEBUG populateExecutiveName ===');
    console.log('currentUserName:', currentUserName);
    console.log('window.currentUserName:', window.currentUserName);

    const nameField = document.getElementById('nombre_ejecutivo');
    console.log('nameField encontrado:', nameField);

    if (nameField) {
        // Intentar múltiples fuentes para el nombre del usuario
        let userName = currentUserName ||
                      window.currentUserName ||
                      (window.currentUserId ? 'JOHANNA LISSETE RIGO ESPINOZA' : '') ||
                      'Usuario Ejecutivo';

        console.log('userName a usar:', userName);

        // Forzar el llenado del campo
        nameField.value = userName;
        nameField.setAttribute('value', userName);

        // Disparar evento para asegurar que se registre el cambio
        nameField.dispatchEvent(new Event('input', { bubbles: true }));
        nameField.dispatchEvent(new Event('change', { bubbles: true }));

        console.log('Campo nombre_ejecutivo llenado con:', userName);
        console.log('Valor actual del campo:', nameField.value);
    } else {
        console.log('Campo nombre_ejecutivo no encontrado');
    }
}

// Función para generar un RUT único para pruebas
function generateUniqueTestRut() {
    // Generar un número aleatorio entre ******** y 99999999
    const randomNumber = Math.floor(Math.random() * (99999999 - ******** + 1)) + ********;

    // Calcular dígito verificador
    let sum = 0;
    let multiplier = 2;
    const rutString = randomNumber.toString();

    for (let i = rutString.length - 1; i >= 0; i--) {
        sum += parseInt(rutString[i]) * multiplier;
        multiplier = multiplier === 7 ? 2 : multiplier + 1;
    }

    const remainder = sum % 11;
    const dv = remainder < 2 ? remainder.toString() : (11 - remainder === 10 ? 'K' : (11 - remainder).toString());

    return `${randomNumber}-${dv}`;
}

// Llenar datos de prueba
function fillTestData() {
    const uniqueRut = generateUniqueTestRut();
    const testData = {
        rut_cliente: uniqueRut,
        razon_social: 'EMPRESA EJEMPLO LTDA',
        rubro: 'COMERCIO AL POR MENOR',
        direccion_comercial: 'AV PROVIDENCIA 123, PROVIDENCIA, SANTIAGO',
        telefono_celular: '*********',
        email: '<EMAIL>',
        numero_pos: 'POS123456',
        tipo_cuenta: 'Cuenta Corriente',
        numero_cuenta_bancaria: '********',
        dias_atencion: 'LUNES A VIERNES',
        horario_atencion: '09:00 - 18:00',
        contrata_boleta: 'Si',
        competencia_actual: 'Transbank'
    };
    
    Object.keys(testData).forEach(key => {
        const field = document.getElementById(key);
        if (field) {
            field.value = testData[key];
            // Trigger validation
            field.dispatchEvent(new Event('input'));
        }
    });
    
    showMessage('info', 'Datos de prueba cargados correctamente');
}

// Validar todo el formulario
function validateForm() {
    const form = document.getElementById('inteletGroupProspectForm');
    if (!form) return false;
    
    let isValid = true;
    const requiredFields = form.querySelectorAll('[required]');
    
    requiredFields.forEach(field => {
        if (!validateRequired(field)) {
            isValid = false;
        }
    });
    
    // Validaciones específicas
    const rutField = document.getElementById('rut_cliente');
    if (rutField && !validateRUT(rutField)) {
        isValid = false;
    }
    
    const razonSocialField = document.getElementById('razon_social');
    if (razonSocialField && !validateRazonSocial(razonSocialField)) {
        isValid = false;
    }
    
    const telefonoField = document.getElementById('telefono_celular');
    if (telefonoField && !validateTelefono(telefonoField)) {
        isValid = false;
    }
    
    const emailField = document.getElementById('email');
    if (emailField && !validateEmail(emailField)) {
        isValid = false;
    }
    
    return isValid;
}

// Variable global para controlar envíos múltiples
let isSubmitting = false;

// Manejar guardado del prospecto
async function handleSaveProspect() {
    if (isSubmitting) {
        console.log('Envío en progreso, por favor espere.');
        return;
    }

    // 1. Validar formulario en el cliente primero
    if (!validateForm()) {
        showMessage('error', 'Por favor, corrija los errores marcados en el formulario.');
        return;
    }

    // 2. Configurar estado de "cargando"
    const saveBtn = document.getElementById('saveInteletGroupProspectBtn');
    const form = document.getElementById('inteletGroupProspectForm');
    setLoadingState(saveBtn, true);
    isSubmitting = true;
    showMessage('info', 'Guardando prospecto...');

    // 3. Enviar datos con fetch
    try {
        const formData = new FormData(form);
        formData.append('usuario_id', window.currentUserId);

        // Debug: Verificar archivos en FormData
        const fileInput = document.getElementById('documentos');
        if (fileInput && fileInput.files.length > 0) {
            console.log('Archivos detectados:', fileInput.files.length);
            for (let i = 0; i < fileInput.files.length; i++) {
                console.log(`Archivo ${i}:`, fileInput.files[i].name, fileInput.files[i].size, 'bytes');
            }
        } else {
            console.log('No se detectaron archivos para enviar');
        }

        // Debug: Mostrar contenido de FormData
        console.log('Contenido de FormData:');
        for (let pair of formData.entries()) {
            if (pair[1] instanceof File) {
                console.log(pair[0], '(File):', pair[1].name, pair[1].size, 'bytes');
            } else {
                console.log(pair[0], ':', pair[1]);
            }
        }

        const response = await fetch('guardar_inteletgroup_prospecto.php', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const result = await response.json();

        if (!response.ok) {
            // Si la respuesta no es 2xx, tratarla como un error
            handleServerError(result, response.status);
        } else {
            // Éxito (respuesta 2xx)
            handleSuccess(result);
        }

    } catch (error) {
        console.error('Error en la solicitud fetch:', error);
        showMessage('error', 'Error de conexión. No se pudo contactar al servidor.');
    } finally {
        // 4. Restaurar estado del botón
        setLoadingState(saveBtn, false);
        isSubmitting = false;
    }
}

// Maneja una respuesta exitosa del servidor
function handleSuccess(result) {
    showMessage('success', result.message || '¡Prospecto guardado con éxito!');
    crearNotificacionPrincipal('success', 'Operación Exitosa', result.message);

    // Esperar 2 segundos para que el usuario vea el mensaje, luego limpiar y cerrar
    setTimeout(() => {
        const modal = bootstrap.Modal.getInstance(document.getElementById('inteletGroupProspectModal'));
        modal.hide();
        resetForm(); // Limpiar el formulario para el próximo uso
    }, 2000);
}

// Maneja una respuesta de error del servidor
function handleServerError(result, status) {
    let errorMessage = result.message || 'Ocurrió un error desconocido.';

    // Limpiar errores previos en los campos
    clearFieldErrors();

    if (status === 400 && result.errors) {
        // Errores de validación específicos
        errorMessage = 'Se encontraron errores en el formulario:';
        Object.keys(result.errors).forEach(fieldName => {
            const field = document.getElementById(fieldName);
            const errorText = result.errors[fieldName];
            if (field) {
                setFieldState(field, 'invalid', errorText);
            }
            errorMessage += `\n- ${errorText}`;
        });
        // Enfocar el primer campo con error
        const firstErrorField = document.querySelector('.is-invalid');
        if (firstErrorField) firstErrorField.focus();

    } else if (status === 409 && result.errors && result.errors.rut_cliente) {
        // Error específico de RUT duplicado
        errorMessage = result.message;
        const rutField = document.getElementById('rut_cliente');
        if (rutField) {
            setFieldState(rutField, 'invalid', result.errors.rut_cliente);
            rutField.focus();
        }
    }

    showMessage('error', errorMessage);
}


// Limpia los errores de todos los campos del formulario
function clearFieldErrors() {
    const form = document.getElementById('inteletGroupProspectForm');
    if (!form) return;
    
    const fields = form.querySelectorAll('.is-invalid');
    fields.forEach(field => {
        setFieldState(field, 'neutral');
    });
}


// Establece el estado de carga de un botón
function setLoadingState(button, isLoading) {
    if (!button) return;
    const btnText = button.querySelector('.btn-text');
    const btnLoading = button.querySelector('.btn-loading');

    if (isLoading) {
        button.disabled = true;
        if (btnText) btnText.style.display = 'none';
        if (btnLoading) btnLoading.style.display = 'inline-block';
    } else {
        button.disabled = false;
        if (btnText) btnText.style.display = 'inline-block';
        if (btnLoading) btnLoading.style.display = 'none';
    }
}

// Asegúrate de que la función `handleSaveProspect` esté correctamente asignada al botón
function setupFormEvents() {
    const saveBtn = document.getElementById('saveInteletGroupProspectBtn');
    if (saveBtn) {
        // Remover listener anterior para evitar duplicados si el script se carga varias veces
        saveBtn.removeEventListener('click', handleSaveProspect); 
        saveBtn.addEventListener('click', handleSaveProspect);
    }
    // ... resto de los eventos
}


// Limpiar todos los mensajes
function clearAllMessages() {
    const container = document.getElementById('inteletgroup-message-container');
    const successMsg = document.getElementById('inteletgroup-success-message');
    const errorMsg = document.getElementById('inteletgroup-error-message');
    const loadingMsg = document.getElementById('inteletgroup-loading-message');

    if (container) container.style.display = 'none';
    if (successMsg) successMsg.style.display = 'none';
    if (errorMsg) errorMsg.style.display = 'none';
    if (loadingMsg) loadingMsg.style.display = 'none';

    // Limpiar también notificaciones principales
    const notifContainer = document.getElementById('inteletgroup-notifications-container');
    if (notifContainer) {
        notifContainer.innerHTML = '';
    }
}

// Mostrar mensajes en el formulario modal
function showMessage(type, message) {
    const container = document.getElementById('inteletgroup-message-container');
    const successMsg = document.getElementById('inteletgroup-success-message');
    const errorMsg = document.getElementById('inteletgroup-error-message');
    const loadingMsg = document.getElementById('inteletgroup-loading-message');

    if (!container || !successMsg || !errorMsg || !loadingMsg) {
        console.error('Elementos de mensaje no encontrados');
        return;
    }

    // Ocultar todos los mensajes
    successMsg.style.display = 'none';
    errorMsg.style.display = 'none';
    loadingMsg.style.display = 'none';
    
    // Mostrar el mensaje apropiado
    let targetMsg;
    switch (type) {
        case 'success':
            targetMsg = successMsg;
            break;
        case 'error':
            targetMsg = errorMsg;
            break;
        case 'info':
            targetMsg = loadingMsg;
            break;
    }
    
    if (targetMsg) {
        const messageText = targetMsg.querySelector('.message-text');
        if (messageText) {
            messageText.textContent = message;
        }
        targetMsg.style.display = 'block';
        container.style.display = 'block';
        
        // Hacer scroll al mensaje para asegurarnos que sea visible
        container.scrollIntoView({ behavior: 'smooth', block: 'start' });
        
        // Auto-hide después de 5 segundos para mensajes de éxito
        if (type === 'success') {
            setTimeout(() => {
                container.style.display = 'none';
            }, 5000);
        }
    }
}

// Crear notificación en la página principal (fuera del modal)
function crearNotificacionPrincipal(type, title, message) {
    // Buscar si ya existe el contenedor de notificaciones o crearlo
    let notifContainer = document.getElementById('inteletgroup-notifications-container');
    
    if (!notifContainer) {
        notifContainer = document.createElement('div');
        notifContainer.id = 'inteletgroup-notifications-container';
        notifContainer.style.position = 'fixed';
        notifContainer.style.top = '80px';
        notifContainer.style.right = '20px';
        notifContainer.style.zIndex = '9999';
        notifContainer.style.maxWidth = '350px';
        document.body.appendChild(notifContainer);
    }
    
    // Crear la notificación
    const notif = document.createElement('div');
    notif.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} shadow-sm`;
    notif.style.marginBottom = '10px';
    notif.style.position = 'relative';
    notif.style.animation = 'fadeInRight 0.5s';
    
    // Agregar estilos de animación si no existen
    if (!document.getElementById('notif-animations')) {
        const style = document.createElement('style');
        style.id = 'notif-animations';
        style.textContent = `
            @keyframes fadeInRight {
                from { opacity: 0; transform: translateX(50px); }
                to { opacity: 1; transform: translateX(0); }
            }
            @keyframes fadeOut {
                from { opacity: 1; }
                to { opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }
    
    // Contenido de la notificación
    notif.innerHTML = `
        <div class="d-flex align-items-center">
            <div class="me-3">
                <i class="bi ${type === 'success' ? 'bi-check-circle-fill' : 
                          type === 'error' ? 'bi-exclamation-triangle-fill' : 
                          'bi-info-circle-fill'}" style="font-size: 1.5rem;"></i>
            </div>
            <div>
                <h6 class="alert-heading mb-1">${title}</h6>
                <p class="mb-0 small">${message}</p>
            </div>
        </div>
        <button type="button" class="btn-close btn-sm position-absolute" 
                style="top: 10px; right: 10px;" aria-label="Close"></button>
    `;
    
    // Agregar la notificación al contenedor
    notifContainer.appendChild(notif);
    
    // Agregar evento para cerrar la notificación
    const closeBtn = notif.querySelector('.btn-close');
    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            notif.style.animation = 'fadeOut 0.3s';
            setTimeout(() => {
                notifContainer.removeChild(notif);
                // Si no quedan notificaciones, remover el contenedor
                if (notifContainer.children.length === 0) {
                    document.body.removeChild(notifContainer);
                }
            }, 300);
        });
    }
    
    // Auto-ocultar después de 8 segundos
    setTimeout(() => {
        if (notif.parentNode) {
            notif.style.animation = 'fadeOut 0.3s';
            setTimeout(() => {
                if (notif.parentNode) {
                    notifContainer.removeChild(notif);
                    // Si no quedan notificaciones, remover el contenedor
                    if (notifContainer.children.length === 0 && notifContainer.parentNode) {
                        document.body.removeChild(notifContainer);
                    }
                }
            }, 300);
        }
    }, 8000);
    
    return notif;
}

// Resetear formulario
function resetForm() {
    const form = document.getElementById('inteletGroupProspectForm');
    if (form) {
        form.reset();
        
        // Limpiar estados de validación
        const fields = form.querySelectorAll('.form-control, .form-select');
        fields.forEach(field => {
            field.classList.remove('is-valid', 'is-invalid');
        });
        
        // Limpiar mensajes
        const feedbacks = form.querySelectorAll('.invalid-feedback');
        feedbacks.forEach(feedback => {
            feedback.textContent = '';
        });
        
        // Resetear el botón de guardar
        const saveBtn = document.getElementById('saveInteletGroupProspectBtn');
        if (saveBtn) {
            saveBtn.disabled = false;
            saveBtn.classList.remove('loading');
            
            const btnText = saveBtn.querySelector('.btn-text');
            const btnLoading = saveBtn.querySelector('.btn-loading');
            if (btnText) btnText.style.display = 'inline-block';
            if (btnLoading) btnLoading.style.display = 'none';
        }
    }
    
    // Ocultar mensajes
    const container = document.getElementById('inteletgroup-message-container');
    if (container) {
        container.style.display = 'none';
    }
}

// Función para abrir el modal (llamada desde el botón)
function abrirModalInteletGroupProspecto() {
    const modal = new bootstrap.Modal(document.getElementById('inteletGroupProspectModal'));
    modal.show();

    // Asegurar que el nombre del ejecutivo se llene después de abrir el modal
    setTimeout(() => {
        populateExecutiveName();
    }, 100);
}

// Función para generar RUT aleatorio válido
function generarRutAleatorio() {
    // Generar número base aleatorio entre 10.000.000 y 99.999.999
    const numeroBase = Math.floor(Math.random() * ********) + ********;

    // Calcular dígito verificador
    const digitoVerificador = calcularDigitoVerificador(numeroBase);

    // Formatear RUT
    const rutGenerado = numeroBase + '-' + digitoVerificador;

    // Asignar al campo
    const campoRut = document.getElementById('rut_cliente');
    if (campoRut) {
        campoRut.value = rutGenerado;
        campoRut.classList.remove('is-invalid');

        // Mostrar notificación temporal
        const button = event.target.closest('button');
        const originalContent = button.innerHTML;
        button.innerHTML = '<i class="bi bi-check"></i>';
        button.classList.add('btn-success');
        button.classList.remove('btn-outline-secondary');

        setTimeout(() => {
            button.innerHTML = originalContent;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 1000);
    }

    console.log('RUT aleatorio generado:', rutGenerado);
}

// Función para calcular dígito verificador de RUT
function calcularDigitoVerificador(rut) {
    let suma = 0;
    let multiplicador = 2;

    // Convertir a string y procesar de derecha a izquierda
    const rutStr = rut.toString();
    for (let i = rutStr.length - 1; i >= 0; i--) {
        suma += parseInt(rutStr[i]) * multiplicador;
        multiplicador = multiplicador === 7 ? 2 : multiplicador + 1;
    }

    const resto = suma % 11;
    const dv = 11 - resto;

    if (dv === 11) return '0';
    if (dv === 10) return 'K';
    return dv.toString();
}

// Función para llenar datos de prueba
function llenarDatosPrueba() {
    // Generar RUT aleatorio primero
    generarRutAleatorio();

    // Datos de prueba para InteletGroup
    const datosPrueba = {
        nombre_ejecutivo: window.currentUserName || 'Ejecutivo Test',
        razon_social: 'EMPRESA EJEMPLO LTDA',
        rubro: 'Comercio',
        direccion_comercial: 'Av. Providencia 1234, Santiago',
        telefono_celular: '9' + Math.floor(Math.random() * ******** + ********),
        email: '<EMAIL>',
        numero_pos: 'POS' + Math.floor(Math.random() * 900000 + 100000),
        tipo_cuenta: 'Cuenta Corriente',
        numero_cuenta_bancaria: Math.floor(Math.random() * ********** + **********).toString(),
        dias_atencion: 'Lunes a Viernes',
        horario_atencion: '09:00 - 18:00',
        contrata_boleta: 'Boleta',
        competencia_actual: 'Transbank'
    };

    // Llenar los campos del formulario
    Object.keys(datosPrueba).forEach(campo => {
        const elemento = document.getElementById('inteletgroup_' + campo) || document.getElementById(campo);
        if (elemento) {
            elemento.value = datosPrueba[campo];
        }
    });

    console.log('Datos de prueba cargados para InteletGroup');
}
